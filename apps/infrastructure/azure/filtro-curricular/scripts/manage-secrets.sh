#!/bin/bash

# =============================================================================
# Azure Key Vault Secrets Management Script - Enhanced Version
# =============================================================================
# This script manages secrets for the Filtro Curricular application with
# full CI/CD pipeline integration and multi-environment support.
#
# Author: Augment Agent
# Version: 2.0
# Date: 2025-06-18
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION AND CONSTANTS
# =============================================================================

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TERRAFORM_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# Parse arguments to determine environment and command
VALID_ENVIRONMENTS=("dev" "staging" "uat" "prod")
ENVIRONMENT="dev"  # Default environment

# Check if first argument is an environment or command
if [[ $# -gt 0 ]]; then
    if [[ " ${VALID_ENVIRONMENTS[@]} " =~ " ${1} " ]]; then
        # First argument is an environment
        ENVIRONMENT="$1"
        shift  # Remove environment from arguments
    elif [[ "$1" == "help" ]] || [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
        # Help requested - don't validate environment
        ENVIRONMENT="dev"  # Use default for help
    fi
fi

RESOURCE_GROUP="filtro-curricular-rg-${ENVIRONMENT}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to get Key Vault name with enhanced discovery
get_keyvault_name() {
    print_status "Finding Key Vault in resource group: $RESOURCE_GROUP"

    # First, check if resource group exists
    if ! az group show --name "$RESOURCE_GROUP" &>/dev/null; then
        print_error "Resource group not found: $RESOURCE_GROUP"
        print_error "Available resource groups:"
        az group list --query "[?contains(name, 'filtro-curricular')].name" -o tsv
        print_error "Make sure Terraform has been applied for environment: $ENVIRONMENT"
        exit 1
    fi

    # Find Key Vault in the resource group
    KEY_VAULT_NAME=$(az keyvault list \
        --resource-group "$RESOURCE_GROUP" \
        --query "[0].name" \
        --output tsv 2>/dev/null)

    if [ -z "$KEY_VAULT_NAME" ] || [ "$KEY_VAULT_NAME" = "null" ]; then
        print_error "No Key Vault found in resource group $RESOURCE_GROUP"
        print_error "Available Key Vaults in subscription:"
        az keyvault list --query "[?contains(name, 'filtro')].{Name:name, ResourceGroup:resourceGroup}" -o table
        print_error "Make sure Terraform has been applied and the infrastructure exists"
        exit 1
    fi

    print_status "Found Key Vault: $KEY_VAULT_NAME"
    echo "$KEY_VAULT_NAME"
}

# Function to get infrastructure info from Terraform
get_terraform_info() {
    print_status "Getting infrastructure information from Terraform..."

    cd "$TERRAFORM_DIR"

    # Check if Terraform is initialized
    if [[ ! -d ".terraform" ]]; then
        print_warning "Terraform not initialized. Initializing..."
        if ! terraform init &>/dev/null; then
            print_error "Failed to initialize Terraform"
            return 1
        fi
    fi

    # Get Terraform outputs if state exists
    if terraform state list &>/dev/null && [[ $(terraform state list | wc -l) -gt 0 ]]; then
        export TF_RESOURCE_GROUP=$(terraform output -raw resource_group_name 2>/dev/null || echo "")
        export TF_KEY_VAULT_NAME=$(terraform output -raw key_vault_name 2>/dev/null || echo "")
        export TF_BACKEND_APP_NAME=$(terraform output -raw backend_app_service_name 2>/dev/null || echo "")
        export TF_FRONTEND_APP_NAME=$(terraform output -raw frontend_app_service_name 2>/dev/null || echo "")

        if [[ -n "$TF_RESOURCE_GROUP" ]]; then
            RESOURCE_GROUP="$TF_RESOURCE_GROUP"
            print_status "Using Terraform resource group: $RESOURCE_GROUP"
        fi

        if [[ -n "$TF_KEY_VAULT_NAME" ]]; then
            print_status "Using Terraform Key Vault: $TF_KEY_VAULT_NAME"
        fi
    else
        print_warning "No Terraform state found. Using expected resource names."
    fi
}

# Function to check if user is logged in to Azure
check_azure_login() {
    print_status "Checking Azure CLI authentication..."
    
    if ! az account show &>/dev/null; then
        print_error "Not logged in to Azure CLI"
        print_status "Please run: az login"
        exit 1
    fi
    
    SUBSCRIPTION=$(az account show --query "name" --output tsv)
    print_status "Logged in to subscription: $SUBSCRIPTION"
}

# Function to validate secret format with enhanced validation
validate_openai_key() {
    local key=$1
    local key_type="${2:-api-key}"

    case "$key_type" in
        "api-key")
            if [[ $key =~ ^sk-proj-[a-zA-Z0-9_-]{64}$ ]]; then
                return 0
            elif [[ $key =~ ^sk-[a-zA-Z0-9_-]{48}$ ]]; then
                print_warning "This appears to be an older format OpenAI key. Consider using a project-based key (sk-proj-...)"
                return 0
            else
                print_warning "OpenAI API key should start with 'sk-proj-' (recommended) or 'sk-' and be the correct length"
                print_warning "Expected format: sk-proj-[64 characters] or sk-[48 characters]"
                return 1
            fi
            ;;
        "token")
            if [[ $key =~ ^sk-[a-zA-Z0-9_-]{48,64}$ ]]; then
                return 0
            else
                print_warning "OpenAI token should start with 'sk-' and be 48-64 characters long"
                return 1
            fi
            ;;
    esac
}

validate_assistant_id() {
    local id=$1
    if [[ $id =~ ^asst_[a-zA-Z0-9]{24}$ ]]; then
        return 0
    else
        print_warning "Assistant ID should start with 'asst_' followed by exactly 24 alphanumeric characters"
        print_warning "Expected format: asst_[24 characters]"
        return 1
    fi
}

# Function to verify secret against GitHub Actions expectations
verify_github_integration() {
    local vault_name=$1

    print_header "Verifying GitHub Actions Integration"

    # Check if secrets match GitHub Actions workflow expectations
    local github_secrets=("OPENAI_API_KEY" "OPENAI_TOKEN" "ASSISTANT_ID_JURIDICO" "ASSISTANT_ID_CALIDAD")
    local keyvault_secrets=("openai-api-key" "openai-token" "assistant-id-juridico" "assistant-id-calidad")

    print_status "Checking alignment between Key Vault secrets and GitHub Actions workflow..."

    for i in "${!github_secrets[@]}"; do
        local github_secret="${github_secrets[$i]}"
        local kv_secret="${keyvault_secrets[$i]}"

        if az keyvault secret show --vault-name "$vault_name" --name "$kv_secret" --query "name" -o tsv &>/dev/null; then
            local secret_value=$(az keyvault secret show --vault-name "$vault_name" --name "$kv_secret" --query "value" -o tsv 2>/dev/null)
            if [[ "$secret_value" == *"placeholder"* ]] || [[ "$secret_value" == *"will-be-set"* ]]; then
                print_warning "Secret '$kv_secret' contains placeholder value"
                print_warning "This suggests GitHub secret '$github_secret' is not configured"
                print_status "Configure in GitHub: Repository → Settings → Environments → $ENVIRONMENT → Secrets"
            else
                print_status "✅ Secret '$kv_secret' has real value (mapped from GitHub '$github_secret')"
            fi
        else
            print_error "❌ Missing Key Vault secret: $kv_secret"
        fi
    done
}

# Function to set a secret with validation
set_secret() {
    local vault_name=$1
    local secret_name=$2
    local secret_value=$3
    local description=$4

    if [ -z "$secret_value" ]; then
        print_error "Secret value cannot be empty for $secret_name"
        return 1
    fi

    print_status "Setting secret: $secret_name ($description)"

    # Store the complete command in a variable for transparency and debugging
    local keyvault_command="az keyvault secret set --vault-name \"$vault_name\" --name \"$secret_name\" --value \"[REDACTED]\" --description \"$description\" --output none"

    # Echo the command for debugging (with redacted value for security)
    print_status "Executing command: $keyvault_command"

    # Execute the actual command and capture the exit code
    az keyvault secret set \
        --vault-name "$vault_name" \
        --name "$secret_name" \
        --value "$secret_value" \
        --description "$description" \
        --output none

    # Capture the exit code in a variable
    local exit_code=$?

    # Use the captured exit code to determine success/failure
    if [ $exit_code -eq 0 ]; then
        print_status "✅ Successfully set secret: $secret_name"
    else
        print_error "❌ Failed to set secret: $secret_name (exit code: $exit_code)"
        return 1
    fi
}

# Function to list all secrets
list_secrets() {
    local vault_name=$1
    
    print_header "Current Secrets in Key Vault: $vault_name"
    
    az keyvault secret list \
        --vault-name "$vault_name" \
        --query "[].{Name:name, Enabled:attributes.enabled, Updated:attributes.updated}" \
        --output table
}

# Function to show secret metadata (without value)
show_secret_info() {
    local vault_name=$1
    local secret_name=$2
    
    print_status "Secret information for: $secret_name"
    
    az keyvault secret show \
        --vault-name "$vault_name" \
        --name "$secret_name" \
        --query "{Name:name, Enabled:attributes.enabled, Created:attributes.created, Updated:attributes.updated, ContentType:contentType}" \
        --output table
}

# Function to update OpenAI secrets interactively
update_openai_secrets() {
    local vault_name=$1
    
    print_header "Updating OpenAI Secrets"
    print_warning "You will be prompted to enter each secret value"
    print_warning "Secret values will not be displayed on screen"
    echo
    
    # OpenAI API Key
    while true; do
        echo -n "Enter OpenAI API Key (sk-proj-... or sk-...): "
        read -s openai_api_key
        echo
        if validate_openai_key "$openai_api_key" "api-key"; then
            break
        fi
        print_error "Invalid format. Please try again."
        echo "Expected: sk-proj-[64 characters] (recommended) or sk-[48 characters]"
    done

    # OpenAI Token
    while true; do
        echo -n "Enter OpenAI Token (starts with sk-): "
        read -s openai_token
        echo
        if validate_openai_key "$openai_token" "token"; then
            break
        fi
        print_error "Invalid format. Please try again."
        echo "Expected: sk-[48-64 characters]"
    done
    
    # Assistant ID Juridico
    while true; do
        echo -n "Enter Assistant ID for Juridico (starts with asst_): "
        read -s assistant_id_juridico
        echo
        if validate_assistant_id "$assistant_id_juridico"; then
            break
        fi
        print_error "Invalid format. Please try again."
    done
    
    # Assistant ID Calidad
    while true; do
        echo -n "Enter Assistant ID for Calidad (starts with asst_): "
        read -s assistant_id_calidad
        echo
        if validate_assistant_id "$assistant_id_calidad"; then
            break
        fi
        print_error "Invalid format. Please try again."
    done
    
    echo
    print_status "Updating secrets in Key Vault..."
    
    # Set all secrets
    set_secret "$vault_name" "openai-api-key" "$openai_api_key" "OpenAI API Key for GPT models"
    set_secret "$vault_name" "openai-token" "$openai_token" "OpenAI Token for assistant API"
    set_secret "$vault_name" "assistant-id-juridico" "$assistant_id_juridico" "OpenAI Assistant ID for Juridico domain"
    set_secret "$vault_name" "assistant-id-calidad" "$assistant_id_calidad" "OpenAI Assistant ID for Calidad domain"
    
    print_status "✅ All OpenAI secrets updated successfully!"
}

# Function to update secrets from file
update_secrets_from_file() {
    local vault_name=$1
    local secrets_file=$2
    print_status "Updating secrets from file: $secrets_file"
    
    if [ ! -f "$secrets_file" ]; then
        print_error "Secrets file not found: $secrets_file"
        exit 1
    fi
    
    print_header "Updating Secrets from File: $secrets_file"
    
    # Read and validate JSON
    if ! jq empty "$secrets_file" 2>/dev/null; then
        print_error "Invalid JSON format in secrets file"
        exit 1
    fi
    
    # Extract values
    openai_api_key=$(jq -r '.openai_api_key // empty' "$secrets_file")
    openai_token=$(jq -r '.openai_token // empty' "$secrets_file")
    assistant_id_juridico=$(jq -r '.assistant_id_juridico // empty' "$secrets_file")
    assistant_id_calidad=$(jq -r '.assistant_id_calidad // empty' "$secrets_file")
    
    # Validate and set secrets
    if [ -n "$openai_api_key" ] && validate_openai_key "$openai_api_key" "api-key"; then
        set_secret "$vault_name" "openai-api-key" "$openai_api_key" "OpenAI API Key for GPT models"
    fi

    if [ -n "$openai_token" ] && validate_openai_key "$openai_token" "token"; then
        set_secret "$vault_name" "openai-token" "$openai_token" "OpenAI Token for assistant API"
    fi

    if [ -n "$assistant_id_juridico" ] && validate_assistant_id "$assistant_id_juridico"; then
        set_secret "$vault_name" "assistant-id-juridico" "$assistant_id_juridico" "OpenAI Assistant ID for Juridico domain"
    fi

    if [ -n "$assistant_id_calidad" ] && validate_assistant_id "$assistant_id_calidad"; then
        set_secret "$vault_name" "assistant-id-calidad" "$assistant_id_calidad" "OpenAI Assistant ID for Calidad domain"
    fi

    print_status "✅ Secrets updated from file successfully!"
}

# Function to verify app service integration
verify_app_service_integration() {
    local vault_name=$1

    print_header "Verifying App Service Integration"

    if [[ -n "$TF_BACKEND_APP_NAME" ]] && [[ -n "$RESOURCE_GROUP" ]]; then
        print_status "Checking backend app service Key Vault integration..."

        # Check app settings with Key Vault references
        local kv_settings=$(az webapp config appsettings list \
            --name "$TF_BACKEND_APP_NAME" \
            --resource-group "$RESOURCE_GROUP" \
            --query "[?contains(value, '@Microsoft.KeyVault')].{Name:name, Value:value}" \
            --output json 2>/dev/null || echo "[]")

        if [[ "$kv_settings" != "[]" ]]; then
            print_status "✅ Key Vault references found in app settings:"
            echo "$kv_settings" | jq -r '.[] | "  \(.Name): \(.Value)"'
        else
            print_warning "⚠️  No Key Vault references found in app settings"
        fi

        # Check managed identity
        local identity=$(az webapp identity show \
            --name "$TF_BACKEND_APP_NAME" \
            --resource-group "$RESOURCE_GROUP" \
            --query "type" -o tsv 2>/dev/null || echo "None")

        if [[ "$identity" == "SystemAssigned" ]]; then
            print_status "✅ Backend app has system-assigned managed identity"
        else
            print_warning "⚠️  Backend app managed identity not configured properly"
        fi
    else
        print_warning "Backend app service information not available"
    fi
}

# Function to test secret accessibility
test_secret_access() {
    local vault_name=$1

    print_header "Testing Secret Accessibility"

    local secrets=("openai-api-key" "openai-token" "assistant-id-juridico" "assistant-id-calidad")

    for secret in "${secrets[@]}"; do
        if az keyvault secret show --vault-name "$vault_name" --name "$secret" --query "name" -o tsv &>/dev/null; then
            print_status "✅ Can access secret: $secret"
        else
            print_error "❌ Cannot access secret: $secret"
        fi
    done
}

# Main script logic with enhanced functionality
main() {
    # Parse command line arguments first to handle help
    local command="${1:-help}"

    # Show help if no arguments or help requested
    if [[ $# -eq 0 ]] || [[ "$command" == "help" ]] || [[ "$command" == "--help" ]] || [[ "$command" == "-h" ]]; then
        show_help
        exit 0
    fi

    print_header "🔐 Azure Key Vault Secrets Management - Environment: $ENVIRONMENT"

    # Check prerequisites
    check_azure_login

    # Get Terraform infrastructure info
    get_terraform_info

    # Get Key Vault name
    KEY_VAULT_NAME=$(get_keyvault_name)

    # Execute command
    case "$command" in
        "list")
            list_secrets "$KEY_VAULT_NAME"
            ;;
        "update")
            update_openai_secrets "$KEY_VAULT_NAME"
            verify_github_integration "$KEY_VAULT_NAME"
            verify_app_service_integration "$KEY_VAULT_NAME"
            ;;
        "update-from-file")
            if [ -z "$2" ]; then
                print_error "Usage: $0 [environment] update-from-file <secrets-file.json>"
                exit 1
            fi
            update_secrets_from_file "$KEY_VAULT_NAME" "$2"
            verify_github_integration "$KEY_VAULT_NAME"
            ;;
        "info")
            if [ -z "$2" ]; then
                print_error "Usage: $0 [environment] info <secret-name>"
                exit 1
            fi
            show_secret_info "$KEY_VAULT_NAME" "$2"
            ;;
        "verify")
            verify_github_integration "$KEY_VAULT_NAME"
            verify_app_service_integration "$KEY_VAULT_NAME"
            test_secret_access "$KEY_VAULT_NAME"
            ;;
        "test")
            test_secret_access "$KEY_VAULT_NAME"
            ;;
        *)
            print_error "Unknown command: $command"
            print_status "Run '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Function to show enhanced help
show_help() {
    echo "🔐 Azure Key Vault Secrets Management Script - Enhanced Version"
    echo
    echo "Usage: $0 [ENVIRONMENT] <command> [options]"
    echo
    echo "ENVIRONMENTS:"
    echo "  dev       Development environment (default)"
    echo "  staging   Staging environment"
    echo "  uat       UAT environment"
    echo "  prod      Production environment"
    echo
    echo "COMMANDS:"
    echo "  list                     List all secrets in Key Vault"
    echo "  update                   Interactively update OpenAI secrets"
    echo "  update-from-file <file>  Update secrets from JSON file"
    echo "  info <secret-name>       Show information about a specific secret"
    echo "  verify                   Verify GitHub Actions and App Service integration"
    echo "  test                     Test secret accessibility"
    echo "  help                     Show this help message"
    echo
    echo "EXAMPLES:"
    echo "  $0 dev list                              # List secrets in dev environment"
    echo "  $0 staging update                        # Update secrets in staging"
    echo "  $0 prod update-from-file secrets.json    # Update prod from file"
    echo "  $0 dev info openai-api-key              # Show info about specific secret"
    echo "  $0 dev verify                           # Verify CI/CD integration"
    echo
    echo "INTEGRATION:"
    echo "  • Automatically discovers resources from Terraform state"
    echo "  • Validates alignment with GitHub Actions workflows"
    echo "  • Verifies App Service Key Vault integration"
    echo "  • Enhanced validation for OpenAI credential formats"
    echo
    echo "TROUBLESHOOTING:"
    echo "  • If HTTP 503 errors: Check GitHub environment secrets are configured"
    echo "  • If Key Vault not found: Ensure Terraform has been applied"
    echo "  • If access denied: Verify Azure CLI authentication and permissions"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Run main function with all arguments
main "$@"
